import asyncio

import aiohttp

from config.run_config import RUN_CONFIG_DICT, DOMAIN, API_ACCESS_TOKEN
from core.schema.chat_request import ChatRequest, Message
from core.schema.chat_response import EventType, ChatResponse, ChatResponseData
from util.common_util import is_empty, decode_sse, pprint

HOST_PROJECT_DIR = "/home/<USER>/workspace/inference"


# ToDo(hm): 流式转批式处理，但没法看出来动态轮播的效果了，感受可能和线上有区别
async def async_process_chat_request_530(chat_request: ChatRequest, env):
    """Async function to process a chat request and return the response text, whether it has item_list, and the item_list"""
    url = f"http://{RUN_CONFIG_DICT[env][DOMAIN]}/api/v1/chat"
    headers = {
        "Connection": "keep-alive",
        "Content-Type": "application/json",
        "Authorization": RUN_CONFIG_DICT[env][API_ACCESS_TOKEN],
        "X-Request-ID": "streamlit",
    }

    # Convert the ChatRequest to a dictionary for the API request
    data = chat_request.to_dict()
    # 只取最后的 event=3 时的数据
    chat_request.debug = True

    response = None
    async with aiohttp.ClientSession() as session:
        async with session.post(url, headers=headers, json=data) as response:
            if response.status != 200:
                response = ChatResponse(
                    event=EventType.FINISH_EVENT,
                    data=ChatResponseData(
                        text=f"请求失败，状态码: {response.status},响应内容:{await response.text()}"
                    )
                )
                return response

            async for line in response.content:
                # 去除多余的新行和空行
                chunk = line.decode("utf-8").strip()
                if is_empty(chunk):
                    continue

                response_dict = decode_sse(chunk)
                if is_empty(response_dict) or "event" not in response_dict:
                    continue

                response = ChatResponse.from_dict(response_dict)
                if response.event == EventType.FINISH_EVENT:
                    print(f"chat response")
                    pprint(response.to_dict())
                    return response

    return response


def enhance_msg(response: ChatResponse):
    response_data = response.data
    first_token_elapse = (response_data.answer_start_time - response_data.request_receive_time) / 1000
    answer_elapse = (response_data.answer_finish_time - response_data.answer_start_time) / 1000
    answer_type = response_data.answer_type
    answer_type_chinese = answer_type.description
    suffix_info_list = [
        f"开始回答耗时={first_token_elapse:.2f}秒",
        f"回答耗时={answer_elapse}秒",
        f"model_version={response_data.model_version}",
        f"response_id={response.request_id}",
        f"回答消耗总tokens={response_data.total_tokens}",
        f"回答类型={answer_type.value}:{answer_type_chinese}",
        f"selected_item={response_data.selected_item}",
        f"item_list={response_data.item_list}",
        f"time_cost={response_data.time_cost}"
    ]
    suffix_info = ",".join(suffix_info_list)
    # 实际前端只需要一个 \n 就能换行（非标准 mk），st 用的是标准 mk，需要两个 \n 才能换行
    added_text = response_data.text.replace("\n", "\n\n")
    # 前端用 %% 表示引用，标准的 mk 用 >
    added_text = added_text.replace("%%", ">")
    return f"{added_text}({suffix_info})"


def response_data_to_message(response_data: ChatResponseData) -> Message:
    """Convert ChatResponseData to a Message object

    This function maps the fields from ChatResponseData to Message:
    - type = answer_type
    - content = text
    - selected_item and item_list are directly mapped

    Returns:
        Message: A Message object with data from the ChatResponseData
    """
    # Convert Item objects to dictionaries to avoid validation errors
    selected_item_dict = None
    if response_data.selected_item:
        selected_item_dict = response_data.selected_item.model_dump()

    item_list_dict = None
    if response_data.item_list:
        item_list_dict = [item.model_dump() for item in response_data.item_list]

    return Message(
        type=response_data.answer_type,
        content=response_data.text,
        selected_item=selected_item_dict,
        item_list=item_list_dict
    )


def call_chat(chat_request: ChatRequest, env="local"):
    """Process a chat request and return the response text, whether it has item_list, the item_list, and answer_type"""
    # Run the async function in a synchronous context
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        print("chat request:")
        pprint(chat_request.to_dict())
        return loop.run_until_complete(async_process_chat_request_530(chat_request, env))
    finally:
        loop.close()
